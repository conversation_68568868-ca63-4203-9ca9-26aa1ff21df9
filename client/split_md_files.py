#!/usr/bin/env python3
"""
Script to split large markdown files into smaller parts based on a specific delimiter.

This script divides markdown files larger than 250,000 characters into roughly equal parts
between 125,000 and 250,000 characters each. The splitting occurs exclusively at the delimiter
pattern "\n---\n" (or similar variations with whitespace). The delimiter is removed at the joins
between parts.

The resulting parts are saved alongside the original file with _part1, _part2, etc. suffixes.
"""

import os
import re
import glob
import argparse
import logging
from pathlib import Path
from typing import List, Tuple

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(levelname)s - %(message)s",
    datefmt="%Y-%m-%d %H:%M:%S",
)
logger = logging.getLogger(__name__)

# Constants
MAX_FILE_SIZE = 250000  # Characters - threshold for splitting files
# Target size for each part (middle of 125000-250000 range)
TARGET_PART_SIZE = 187500
MAX_PART_SIZE = 250000  # Maximum allowed size for any part
MIN_PART_SIZE = 125000  # Minimum preferred size for parts
# Pattern to match delimiters like "\n---\n" with optional whitespace
DELIMITER_PATTERN = r"\n\s*---\s*\n"


def count_characters(file_path: str) -> int:
    """
    Count the number of characters in a file.

    Args:
        file_path: Path to the file

    Returns:
        Number of characters in the file
    """
    try:
        with open(file_path, "r", encoding="utf-8") as file:
            content = file.read()
            return len(content)
    except Exception as e:
        logger.error(f"Error reading file {file_path}: {e}")
        return 0


def find_delimiter_positions(content: str) -> List[int]:
    """
    Find all positions of the delimiter in the content.

    Args:
        content: File content as string

    Returns:
        List of positions where delimiters are found
    """
    delimiter_positions = [0]  # Start position

    # Find all occurrences of the delimiter pattern
    for match in re.finditer(DELIMITER_PATTERN, content):
        delimiter_positions.append(match.start())

    # Add the end position
    delimiter_positions.append(len(content))

    return delimiter_positions


def determine_split_points(content: str, delimiter_positions: List[int]) -> List[int]:
    """
    Determine optimal split points based on delimiter positions and target size.

    Args:
        content: File content as string
        delimiter_positions: List of positions where delimiters are found

    Returns:
        List of positions where the file should be split
    """
    total_length = len(content)

    # If the file is small enough, return it as is
    if total_length <= MAX_FILE_SIZE:
        return [0, total_length]

    # Skip the first and last positions (they're just the start and end)
    usable_delimiters = delimiter_positions[1:-1]

    # If we have no usable delimiters, we can't split the file
    if not usable_delimiters:
        logger.warning("No usable delimiters found, cannot split the file")
        return [0, total_length]

    # Calculate minimum number of parts needed to ensure no part exceeds MAX_PART_SIZE
    min_parts_needed = (
        total_length + MAX_PART_SIZE - 1
    ) // MAX_PART_SIZE  # Ceiling division

    # Calculate ideal number of parts based on TARGET_PART_SIZE
    ideal_parts = max(
        min_parts_needed, (total_length + TARGET_PART_SIZE - 1) // TARGET_PART_SIZE
    )

    logger.debug(
        f"File size: {total_length}, minimum parts needed: {min_parts_needed}, ideal parts: {ideal_parts}"
    )

    # Calculate the ideal part size
    ideal_part_size = total_length / ideal_parts

    # Strategy: Try to create roughly equal parts using only the delimiters
    split_points = [0]  # Start with the beginning of the file
    next_ideal_position = ideal_part_size

    while (
        next_ideal_position < total_length - MIN_PART_SIZE
    ):  # Ensure last part is not too small
        # Find the delimiter closest to the ideal position
        best_delimiter = None
        best_distance = float("inf")

        for pos in usable_delimiters:
            # Skip positions we've already passed
            if pos <= split_points[-1]:
                continue

            # Skip positions that would create a part smaller than MIN_PART_SIZE
            if pos - split_points[-1] < MIN_PART_SIZE:
                continue

            # Skip positions that would create a part larger than MAX_PART_SIZE
            if pos - split_points[-1] > MAX_PART_SIZE:
                # If we can't find any delimiter within MAX_PART_SIZE, we'll have to use
                # the closest one that doesn't exceed MAX_PART_SIZE
                continue

            # Calculate distance to ideal position
            distance = abs(pos - (split_points[-1] + ideal_part_size))

            if distance < best_distance:
                best_distance = distance
                best_delimiter = pos

        # If we found a suitable delimiter, use it
        if best_delimiter is not None:
            split_points.append(best_delimiter)
            next_ideal_position = split_points[-1] + ideal_part_size
        else:
            # If we can't find a suitable delimiter, we have to stop
            # This means we can't split the file according to the requirements
            logger.warning("Could not find suitable delimiters for splitting")
            break

    # Add the end of the file if it's not already included
    if split_points[-1] != total_length:
        split_points.append(total_length)

    # If we couldn't create at least two parts, return the whole file
    if len(split_points) <= 2:
        logger.warning("Could not split the file into multiple parts using delimiters")
        return [0, total_length]

    # Verify no part exceeds MAX_PART_SIZE
    for i in range(len(split_points) - 1):
        part_size = split_points[i + 1] - split_points[i]
        if part_size > MAX_PART_SIZE:
            logger.warning(
                f"Part {i + 1} size ({part_size}) exceeds maximum allowed size ({MAX_PART_SIZE})"
            )

    return split_points


def split_file(file_path: str) -> bool:
    """
    Split a file into parts based on delimiters and size constraints.

    Args:
        file_path: Path to the file to split

    Returns:
        True if the file was split, False otherwise
    """
    try:
        # Check if file needs splitting
        file_size = count_characters(file_path)
        if file_size <= MAX_FILE_SIZE:
            logger.info(
                f"File {file_path} is smaller than {MAX_FILE_SIZE} characters, skipping."
            )
            return False

        logger.info(f"Processing file: {file_path} ({file_size} characters)")

        # Read file content
        with open(file_path, "r", encoding="utf-8") as file:
            content = file.read()

        # Find delimiter positions
        delimiter_positions = find_delimiter_positions(content)
        logger.info(f"Found {len(delimiter_positions) - 2} delimiters in the file")

        # Determine split points
        split_points = determine_split_points(content, delimiter_positions)

        # If we couldn't find good split points, log and return
        if len(split_points) <= 2:
            logger.warning(f"Could not find suitable split points for {file_path}")
            return False

        # Split the file and save parts
        file_path_obj = Path(file_path)
        base_name = file_path_obj.stem
        extension = file_path_obj.suffix
        directory = file_path_obj.parent

        # Check if any part exceeds the maximum size
        for i in range(len(split_points) - 1):
            part_size = split_points[i + 1] - split_points[i]
            if part_size > MAX_PART_SIZE:
                logger.warning(
                    f"Part {i + 1} size ({part_size}) exceeds maximum allowed size ({MAX_PART_SIZE})"
                )
                return False  # Don't proceed if any part is too large

        # Create a list to track if each split point is at a delimiter
        is_at_delimiter = [False] * len(split_points)
        for i, pos in enumerate(split_points):
            if pos in delimiter_positions:
                is_at_delimiter[i] = True

        # Verify all split points (except start and end) are at delimiters
        for i in range(1, len(split_points) - 1):
            if not is_at_delimiter[i]:
                logger.warning(
                    f"Split point at position {split_points[i]} is not at a delimiter"
                )
                return False  # Don't proceed if any split point is not at a delimiter

        for i in range(len(split_points) - 1):
            start = split_points[i]
            end = split_points[i + 1]

            # Extract content for this part
            part_content = content[start:end]

            # Remove delimiter at the beginning of the part (except for the first part)
            if i > 0 and part_content.startswith("\n"):
                # Find the end of the delimiter pattern at the beginning
                match = re.match(r"^\n\s*---\s*\n", part_content)
                if match:
                    part_content = part_content[match.end() :]
                    logger.debug(f"Removed delimiter at the beginning of part {i + 1}")

            # Remove delimiter at the end of the part (except for the last part)
            if i < len(split_points) - 2 and part_content.endswith("\n"):
                # Find the start of the delimiter pattern at the end
                match = re.search(r"\n\s*---\s*\n$", part_content)
                if match:
                    part_content = part_content[: match.start()] + "\n"
                    logger.debug(f"Removed delimiter at the end of part {i + 1}")

            # Create part filename
            part_filename = f"{base_name}_part{i + 1}{extension}"
            part_path = directory / part_filename

            # Save part
            with open(part_path, "w", encoding="utf-8") as part_file:
                part_file.write(part_content)

            logger.info(
                f"Created part {i + 1}: {part_path} ({len(part_content)} characters)"
            )

        return True

    except Exception as e:
        logger.error(f"Error splitting file {file_path}: {e}")
        return False


def process_directory(directory: str, pattern: str = "*.md") -> Tuple[int, int]:
    """
    Process all markdown files in a directory.

    Args:
        directory: Directory to process
        pattern: File pattern to match (default: "*.md")

    Returns:
        Tuple of (number of files processed, number of files split)
    """
    directory_path = Path(directory)
    if not directory_path.exists() or not directory_path.is_dir():
        logger.error(f"Directory {directory} does not exist or is not a directory")
        return 0, 0

    # Find all markdown files in the directory
    file_paths = list(directory_path.glob(pattern))

    if not file_paths:
        logger.warning(f"No files matching pattern '{pattern}' found in {directory}")
        return 0, 0

    logger.info(
        f"Found {len(file_paths)} files matching pattern '{pattern}' in {directory}"
    )

    # Process each file
    files_processed = 0
    files_split = 0

    for file_path in file_paths:
        # Skip files that are already parts
        if re.search(r"_part\d+", file_path.stem):
            logger.info(f"Skipping part file: {file_path}")
            continue

        files_processed += 1
        if split_file(str(file_path)):
            files_split += 1

    return files_processed, files_split


def main():
    """
    Main function to parse arguments and process files.
    """
    parser = argparse.ArgumentParser(
        description="Split large markdown files into smaller parts based on delimiters."
    )
    parser.add_argument(
        "directory",
        nargs="?",
        default=".",
        help="Directory containing markdown files to process (default: current directory)",
    )
    parser.add_argument(
        "--pattern", default="*.md", help="File pattern to match (default: *.md)"
    )
    parser.add_argument("--verbose", action="store_true", help="Enable verbose logging")

    args = parser.parse_args()

    # Set logging level based on verbose flag
    if args.verbose:
        logger.setLevel(logging.DEBUG)

    # Process the directory
    files_processed, files_split = process_directory(args.directory, args.pattern)

    # Print summary
    logger.info(
        f"Summary: Processed {files_processed} files, split {files_split} files"
    )


if __name__ == "__main__":
    main()
