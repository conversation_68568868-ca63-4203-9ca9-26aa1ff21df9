"""
Tests for WebSocket authentication.
"""
import pytest
import asyncio
from fastapi.testclient import Test<PERSON>lient
from unittest.mock import patch, MagicMock

from core.app import create_app
from api.middleware.auth import api_key_manager


@pytest.fixture
def app():
    """Create test app."""
    return create_app()


@pytest.fixture
def client(app):
    """Create test client."""
    return TestClient(app)


@pytest.fixture
def mock_task_queue():
    """Mock task queue for testing."""
    mock_queue = MagicMock()
    mock_queue.active_async_tasks = []
    mock_queue.LIMIT_SUBTITLE_WORKERS = 5
    mock_queue.LIMIT_SUMMARIZE_WORKERS = 5
    return mock_queue


class TestWebSocketAuthentication:
    """Test WebSocket authentication functionality."""

    def test_websocket_auth_disabled(self, client, mock_task_queue):
        """Test WebSocket connection when authentication is disabled."""
        with patch('core.config.get_settings') as mock_settings:
            # Mock settings with auth disabled
            mock_settings.return_value.REQUIRE_AUTH = False
            mock_settings.return_value.WEBSOCKET_PING_INTERVAL = 30
            
            with patch.object(client.app.state, 'task_queue', mock_task_queue):
                with client.websocket_connect("/ws/subtitles") as websocket:
                    # Should connect successfully without authentication
                    assert websocket is not None

    def test_websocket_auth_required_no_token(self, client, mock_task_queue):
        """Test WebSocket connection fails when auth required but no token provided."""
        with patch('core.config.get_settings') as mock_settings:
            # Mock settings with auth enabled
            mock_settings.return_value.REQUIRE_AUTH = True
            mock_settings.return_value.WEBSOCKET_PING_INTERVAL = 30
            
            with patch.object(client.app.state, 'task_queue', mock_task_queue):
                # Should fail to connect without token
                with pytest.raises(Exception):  # WebSocket connection should fail
                    with client.websocket_connect("/ws/subtitles"):
                        pass

    def test_websocket_auth_valid_token_query_param(self, client, mock_task_queue):
        """Test WebSocket connection with valid token in query parameter."""
        # Create a test API key
        test_api_key = api_key_manager.generate_api_key(
            name="Test WebSocket Client",
            permissions=["read", "write"],
            rate_limit=1000
        )
        
        with patch('core.config.get_settings') as mock_settings:
            # Mock settings with auth enabled
            mock_settings.return_value.REQUIRE_AUTH = True
            mock_settings.return_value.WEBSOCKET_PING_INTERVAL = 30
            
            with patch.object(client.app.state, 'task_queue', mock_task_queue):
                # Should connect successfully with valid token
                with client.websocket_connect(f"/ws/subtitles?token={test_api_key}") as websocket:
                    assert websocket is not None

    def test_websocket_auth_invalid_token(self, client, mock_task_queue):
        """Test WebSocket connection fails with invalid token."""
        with patch('core.config.get_settings') as mock_settings:
            # Mock settings with auth enabled
            mock_settings.return_value.REQUIRE_AUTH = True
            mock_settings.return_value.WEBSOCKET_PING_INTERVAL = 30
            
            with patch.object(client.app.state, 'task_queue', mock_task_queue):
                # Should fail to connect with invalid token
                with pytest.raises(Exception):  # WebSocket connection should fail
                    with client.websocket_connect("/ws/subtitles?token=invalid_token"):
                        pass

    def test_websocket_auth_insufficient_permissions(self, client, mock_task_queue):
        """Test WebSocket connection fails with insufficient permissions."""
        # Create a test API key with only read permissions
        test_api_key = api_key_manager.generate_api_key(
            name="Read Only Client",
            permissions=["read"],  # Missing 'write' permission
            rate_limit=1000
        )
        
        with patch('core.config.get_settings') as mock_settings:
            # Mock settings with auth enabled
            mock_settings.return_value.REQUIRE_AUTH = True
            mock_settings.return_value.WEBSOCKET_PING_INTERVAL = 30
            
            with patch.object(client.app.state, 'task_queue', mock_task_queue):
                # Should fail to connect without write permission
                with pytest.raises(Exception):  # WebSocket connection should fail
                    with client.websocket_connect(f"/ws/subtitles?token={test_api_key}"):
                        pass

    def test_websocket_summarize_auth(self, client, mock_task_queue):
        """Test WebSocket summarize endpoint authentication."""
        # Create a test API key with write permissions
        test_api_key = api_key_manager.generate_api_key(
            name="Summarize Client",
            permissions=["read", "write"],
            rate_limit=1000
        )
        
        with patch('core.config.get_settings') as mock_settings:
            # Mock settings with auth enabled
            mock_settings.return_value.REQUIRE_AUTH = True
            mock_settings.return_value.WEBSOCKET_PING_INTERVAL = 30
            
            with patch.object(client.app.state, 'task_queue', mock_task_queue):
                # Should connect successfully to summarize endpoint
                with client.websocket_connect(f"/ws/summarize?token={test_api_key}") as websocket:
                    assert websocket is not None


class TestWebSocketAuthMethods:
    """Test different WebSocket authentication methods."""

    def test_auth_method_detection_query_param(self):
        """Test authentication method detection for query parameter."""
        from api.middleware.auth import get_websocket_user
        
        # Mock WebSocket with query parameter
        mock_websocket = MagicMock()
        mock_websocket.query_params = {"token": "test_api_key"}
        mock_websocket.headers = {}
        mock_websocket.client = "127.0.0.1:12345"
        
        # Create a test API key
        test_api_key = api_key_manager.generate_api_key(
            name="Query Param Test",
            permissions=["read", "write"],
            rate_limit=1000
        )
        mock_websocket.query_params = {"token": test_api_key}
        
        with patch('core.config.get_settings') as mock_settings:
            mock_settings.return_value.REQUIRE_AUTH = True
            
            # Test authentication
            result = asyncio.run(get_websocket_user(mock_websocket))
            assert result is not None
            assert result["auth_method"] == "query_param"
            assert result["name"] == "Query Param Test"

    def test_auth_method_detection_header(self):
        """Test authentication method detection for Authorization header."""
        from api.middleware.auth import get_websocket_user
        
        # Create a test API key
        test_api_key = api_key_manager.generate_api_key(
            name="Header Test",
            permissions=["read", "write"],
            rate_limit=1000
        )
        
        # Mock WebSocket with Authorization header
        mock_websocket = MagicMock()
        mock_websocket.query_params = {}
        mock_websocket.headers = {"authorization": f"Bearer {test_api_key}"}
        mock_websocket.client = "127.0.0.1:12345"
        
        with patch('core.config.get_settings') as mock_settings:
            mock_settings.return_value.REQUIRE_AUTH = True
            
            # Test authentication
            result = asyncio.run(get_websocket_user(mock_websocket))
            assert result is not None
            assert result["auth_method"] == "header"
            assert result["name"] == "Header Test"

    def test_auth_method_detection_subprotocol(self):
        """Test authentication method detection for subprotocol."""
        from api.middleware.auth import get_websocket_user
        
        # Create a test API key
        test_api_key = api_key_manager.generate_api_key(
            name="Subprotocol Test",
            permissions=["read", "write"],
            rate_limit=1000
        )
        
        # Mock WebSocket with subprotocol
        mock_websocket = MagicMock()
        mock_websocket.query_params = {}
        mock_websocket.headers = {
            "sec-websocket-protocol": f"api_key_{test_api_key}, other_protocol"
        }
        mock_websocket.client = "127.0.0.1:12345"
        
        with patch('core.config.get_settings') as mock_settings:
            mock_settings.return_value.REQUIRE_AUTH = True
            
            # Test authentication
            result = asyncio.run(get_websocket_user(mock_websocket))
            assert result is not None
            assert result["auth_method"] == "subprotocol"
            assert result["name"] == "Subprotocol Test"


if __name__ == "__main__":
    pytest.main([__file__])
