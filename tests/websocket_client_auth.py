#!/usr/bin/env python3
"""
Example WebSocket client with authentication for YouTube Subtitles API.

This example demonstrates how to connect to WebSocket endpoints with proper authentication.
"""
import asyncio
import json
import sys
from typing import Optional
import websockets
from websockets.exceptions import ConnectionClosedError


class AuthenticatedWebSocketClient:
    """WebSocket client with authentication support."""
    
    def __init__(self, api_key: str, base_url: str = "ws://localhost:8000"):
        self.api_key = api_key
        self.base_url = base_url
        self.websocket: Optional[websockets.WebSocketServerProtocol] = None
    
    async def connect_subtitles(self) -> bool:
        """Connect to subtitles WebSocket endpoint."""
        uri = f"{self.base_url}/ws/subtitles?token={self.api_key}"
        
        try:
            self.websocket = await websockets.connect(uri)
            print(f"✅ Connected to subtitles WebSocket: {uri}")
            return True
        except ConnectionClosedError as e:
            if e.code == 4001:
                print(f"❌ Authentication failed: {e.reason}")
            elif e.code == 4000:
                print(f"❌ Authentication error: {e.reason}")
            else:
                print(f"❌ Connection failed: {e}")
            return False
        except Exception as e:
            print(f"❌ Connection error: {e}")
            return False
    
    async def connect_summarize(self) -> bool:
        """Connect to summarize WebSocket endpoint."""
        uri = f"{self.base_url}/ws/summarize?token={self.api_key}"
        
        try:
            self.websocket = await websockets.connect(uri)
            print(f"✅ Connected to summarize WebSocket: {uri}")
            return True
        except ConnectionClosedError as e:
            if e.code == 4001:
                print(f"❌ Authentication failed: {e.reason}")
            elif e.code == 4000:
                print(f"❌ Authentication error: {e.reason}")
            else:
                print(f"❌ Connection failed: {e}")
            return False
        except Exception as e:
            print(f"❌ Connection error: {e}")
            return False
    
    async def send_message(self, message: dict):
        """Send a message to the WebSocket."""
        if not self.websocket:
            print("❌ Not connected to WebSocket")
            return
        
        try:
            await self.websocket.send(json.dumps(message))
            print(f"📤 Sent: {message}")
        except Exception as e:
            print(f"❌ Failed to send message: {e}")
    
    async def listen_for_messages(self):
        """Listen for messages from the WebSocket."""
        if not self.websocket:
            print("❌ Not connected to WebSocket")
            return
        
        try:
            async for message in self.websocket:
                data = json.loads(message)
                print(f"📥 Received: {data}")
                
                # Handle ping messages
                if data.get("type") == "ping":
                    pong_message = {
                        "type": "pong",
                        "task_id": data.get("task_id", "initial")
                    }
                    await self.send_message(pong_message)
                
        except ConnectionClosedError as e:
            print(f"🔌 Connection closed: {e}")
        except Exception as e:
            print(f"❌ Error receiving messages: {e}")
    
    async def close(self):
        """Close the WebSocket connection."""
        if self.websocket:
            await self.websocket.close()
            print("🔌 WebSocket connection closed")


async def test_subtitles_websocket(api_key: str):
    """Test subtitles WebSocket endpoint."""
    print("\n🎬 Testing Subtitles WebSocket...")
    
    client = AuthenticatedWebSocketClient(api_key)
    
    if await client.connect_subtitles():
        # Send a YouTube URL for processing
        await client.send_message({
            "url": "https://www.youtube.com/watch?v=dQw4w9WgXcQ"
        })
        
        # Listen for messages for a short time
        try:
            await asyncio.wait_for(client.listen_for_messages(), timeout=10.0)
        except asyncio.TimeoutError:
            print("⏰ Timeout reached, closing connection")
        
        await client.close()


async def test_summarize_websocket(api_key: str):
    """Test summarize WebSocket endpoint."""
    print("\n📝 Testing Summarize WebSocket...")
    
    client = AuthenticatedWebSocketClient(api_key)
    
    if await client.connect_summarize():
        # Send text for summarization
        await client.send_message({
            "type": "text",
            "text": "This is a sample text that needs to be summarized. " * 10,
            "mode": "concise"
        })
        
        # Listen for messages for a short time
        try:
            await asyncio.wait_for(client.listen_for_messages(), timeout=10.0)
        except asyncio.TimeoutError:
            print("⏰ Timeout reached, closing connection")
        
        await client.close()


async def test_authentication_methods(api_key: str):
    """Test different authentication methods."""
    print("\n🔐 Testing Different Authentication Methods...")
    
    base_url = "ws://localhost:8000"
    
    # Method 1: Query parameter (already tested above)
    print("1️⃣ Query parameter method: ✅ (tested above)")
    
    # Method 2: Authorization header (if supported by client)
    print("2️⃣ Authorization header method:")
    try:
        uri = f"{base_url}/ws/subtitles"
        headers = {"Authorization": f"Bearer {api_key}"}
        
        async with websockets.connect(uri, extra_headers=headers) as websocket:
            print("   ✅ Connected with Authorization header")
            await websocket.close()
    except Exception as e:
        print(f"   ❌ Failed with Authorization header: {e}")
    
    # Method 3: Subprotocol
    print("3️⃣ Subprotocol method:")
    try:
        uri = f"{base_url}/ws/subtitles"
        subprotocols = [f"api_key_{api_key}"]
        
        async with websockets.connect(uri, subprotocols=subprotocols) as websocket:
            print("   ✅ Connected with subprotocol")
            await websocket.close()
    except Exception as e:
        print(f"   ❌ Failed with subprotocol: {e}")


async def test_invalid_authentication():
    """Test authentication with invalid credentials."""
    print("\n❌ Testing Invalid Authentication...")
    
    client = AuthenticatedWebSocketClient("invalid_api_key")
    
    print("Testing with invalid API key...")
    success = await client.connect_subtitles()
    if not success:
        print("✅ Correctly rejected invalid API key")
    else:
        print("❌ Should have rejected invalid API key")
        await client.close()


async def main():
    """Main function to run all tests."""
    print("🚀 WebSocket Authentication Test Client")
    print("=" * 50)
    
    # Get API key from command line or use demo key
    if len(sys.argv) > 1:
        api_key = sys.argv[1]
        print(f"Using provided API key: {api_key[:10]}...")
    else:
        # Use demo admin key (should be available in development)
        api_key = "admin_key_67890"
        print(f"Using demo admin key: {api_key}")
        print("💡 You can provide your own API key as command line argument")
    
    try:
        # Test invalid authentication first
        await test_invalid_authentication()
        
        # Test different authentication methods
        await test_authentication_methods(api_key)
        
        # Test subtitles WebSocket
        await test_subtitles_websocket(api_key)
        
        # Test summarize WebSocket
        await test_summarize_websocket(api_key)
        
        print("\n✅ All tests completed!")
        
    except KeyboardInterrupt:
        print("\n⏹️ Tests interrupted by user")
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")


if __name__ == "__main__":
    print("📋 Requirements: pip install websockets")
    print("🔧 Usage: python websocket_client_auth.py [api_key]")
    print()
    
    asyncio.run(main())
