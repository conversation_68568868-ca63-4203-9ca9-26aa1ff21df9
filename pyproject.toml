[project]
name = "yt-subs-api4"
version = "0.1.0"
description = "API for downloading YouTube video subtitles and summarizing text using Gemini AI"
readme = "README.md"
requires-python = ">=3.12"
dependencies = [
    "aiohttp>=3.11.18",
    "fastapi>=0.115.12",
    "loguru>=0.7.3",
    "structlog>=25.3.0",
    "slowapi>=0.1.9",
    "pysocks>=1.7.1",
    "python-dotenv>=1.1.0",
    "uvicorn>=0.34.2",
    "websockets>=15.0.1",
    "yt-dlp>=2025.5.22",
    "google-generativeai>=0.8.4",
    "python-multipart>=0.0.20",
    "sqlalchemy>=2.0.0",
    "rich>=14.0.0",
    "mdformat>=0.7.22",
    "mdformat-gfm>=0.4.1",
    "google>=3.0.0",
    "google-genai>=1.15.0",
    "psycopg2-binary>=2.9.10",
    "psutil>=6.1.0",
    "pydantic-settings>=2.9.1",
    "beautifulsoup4>=4.12.0",
    "lxml>=5.0.0",
    "pyyaml>=6.0.2",
    "prometheus-client>=0.22.1",
    "pytest>=8.4.0",
]

[tool.ruff]
# Exclude common directories
exclude = [
    ".bzr",
    ".direnv",
    ".eggs",
    ".git",
    ".git-rewrite",
    ".hg",
    ".mypy_cache",
    ".nox",
    ".pants.d",
    ".pytype",
    ".ruff_cache",
    ".svn",
    ".tox",
    ".venv",
    "__pypackages__",
    "_build",
    "buck-out",
    "build",
    "dist",
    "node_modules",
    "venv",
]

# Same as Black.
line-length = 88
indent-width = 4

# Assume Python 3.12
target-version = "py312"

[tool.ruff.lint]
# Enable pycodestyle (`E`) and Pyflakes (`F`) codes by default.
select = ["E4", "E7", "E9", "F", "I", "UP", "B", "C4", "PIE", "T20"]
ignore = []

# Allow fix for all enabled rules (when `--fix`) is provided.
fixable = ["ALL"]
unfixable = []

# Allow unused variables when underscore-prefixed.
dummy-variable-rgx = "^(_+|(_+[a-zA-Z0-9_]*[a-zA-Z0-9]+?))$"

[tool.ruff.format]
# Like Black, use double quotes for strings.
quote-style = "double"

# Like Black, indent with spaces, rather than tabs.
indent-style = "space"

# Like Black, respect magic trailing commas.
skip-magic-trailing-comma = false

# Like Black, automatically detect the appropriate line ending.
line-ending = "auto"
