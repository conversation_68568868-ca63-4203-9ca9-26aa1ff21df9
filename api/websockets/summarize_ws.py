"""
WebSocket endpoints for text summarization.
"""

import asyncio
import base64
from fastapi import APIRouter, WebSocket, WebSocketDisconnect
from loguru import logger

from models.schemas import (
    WebSocketSummarizeMessage,
    WebSocketSummarizeRequest,
    MessageType,
    TaskStatus,
)
from models.validators import validate_text_content, validate_filename
from core.config import get_settings
from api.middleware.auth import require_websocket_write, AuthError, ForbiddenError

settings = get_settings()
PING_INTERVAL = settings.WEBSOCKET_PING_INTERVAL

router = APIRouter()


def _check_websocket_overload(task_queue) -> tuple[bool, dict]:
    """Check if server is overloaded for WebSocket connections."""
    active_tasks_count = len(task_queue.active_async_tasks)
    max_total_workers = (
        task_queue.LIMIT_SUBTITLE_WORKERS + task_queue.LIMIT_SUMMARIZE_WORKERS
    )

    if active_tasks_count >= max_total_workers:
        return True, {
            "type": MessageType.STATUS,
            "task_id": "error",
            "status": TaskStatus.FAILED,
            "error": "Server is at maximum processing capacity. Please try again later.",
        }

    return False, {}


def _process_summarize_request(
    request: WebSocketSummarizeRequest, websocket_client
) -> str:
    """Process and validate summarize request, return text to summarize."""
    text_to_summarize = ""

    if request.type == MessageType.TEXT:
        text_to_summarize = request.content
        logger.debug(
            f"Received text input for summarization (length: {len(text_to_summarize)}) from {websocket_client} in /ws/summarize endpoint"
        )

        # Validate text content
        try:
            validated_text = validate_text_content(
                text_to_summarize, min_length=10, max_length=500_000
            )
            text_to_summarize = validated_text
            logger.debug(f"Text content validation passed for {websocket_client}")
        except ValueError as e:
            raise ValueError(f"Text validation failed: {str(e)}")
    elif request.type == MessageType.FILE:
        logger.debug(
            f"Received file input '{request.filename}' for summarization from {websocket_client} in /ws/summarize endpoint"
        )
        if not request.filename:
            raise ValueError("Filename is required for file input.")

        # Validate filename
        try:
            validated_filename = validate_filename(request.filename)
            logger.debug(f"Filename validation passed: {validated_filename}")
        except ValueError as e:
            raise ValueError(f"Invalid filename: {str(e)}")

        file_ext = validated_filename.split(".")[-1].lower()
        if file_ext not in ["txt", "md"]:
            raise ValueError(f"Unsupported file type: {file_ext}. Supported: txt, md")

        try:
            text_to_summarize = base64.b64decode(request.content).decode("utf-8")
            logger.debug(
                f"Decoded file content for '{validated_filename}' (length: {len(text_to_summarize)}) from {websocket_client} in /ws/summarize endpoint"
            )

            # Validate decoded text content
            try:
                validated_text = validate_text_content(
                    text_to_summarize, min_length=10, max_length=500_000
                )
                text_to_summarize = validated_text
                logger.debug(f"File content validation passed for {websocket_client}")
            except ValueError as e:
                raise ValueError(f"File content validation failed: {str(e)}")

        except Exception as e:
            raise ValueError(f"Invalid file content for {validated_filename}: {str(e)}")
    else:
        raise ValueError(f"Unsupported message type: {request.type}")

    if not text_to_summarize.strip():
        raise ValueError("Cannot summarize empty text.")

    return text_to_summarize


@router.websocket("/ws/summarize")
async def websocket_summarize_endpoint(websocket: WebSocket):
    """WebSocket endpoint for real-time text summarization"""
    logger.debug(
        f"New WebSocket connection attempt for /ws/summarize from {websocket.client}"
    )

    # Authenticate user before accepting connection
    user = None
    try:
        user = await require_websocket_write(websocket)
        logger.info(
            f"WebSocket /ws/summarize authenticated: {user['name']} "
            f"(method: {user.get('auth_method', 'unknown')}) from {websocket.client}"
        )
    except (AuthError, ForbiddenError) as e:
        logger.warning(
            f"WebSocket /ws/summarize authentication failed from {websocket.client}: {str(e)}"
        )
        await websocket.close(code=4001, reason=f"Authentication failed: {str(e)}")
        return
    except Exception as e:
        logger.error(
            f"WebSocket /ws/summarize authentication error from {websocket.client}: {str(e)}",
            exc_info=True,
        )
        await websocket.close(code=4000, reason="Authentication error")
        return

    # Get task_queue from app state (will be set during lifespan)
    task_queue = websocket.app.state.task_queue

    # Check server overload
    is_overloaded, error_response = _check_websocket_overload(task_queue)
    if is_overloaded:
        logger.debug(
            f"Overload check failed for /ws/summarize endpoint: Server is at maximum processing capacity."
        )
        await websocket.accept()
        await websocket.send_json(
            WebSocketSummarizeMessage(**error_response).model_dump()
        )
        await websocket.close()
        logger.debug(
            f"WebSocket connection closed due to overload for /ws/summarize from {websocket.client}"
        )
        return

    await websocket.accept()
    logger.debug(
        f"WebSocket connected: {websocket.client} for /ws/summarize endpoint (user: {user['name']})"
    )
    task_id = None
    client_uid_for_error_reporting = None

    try:
        while True:
            try:
                client_message_json = await asyncio.wait_for(
                    websocket.receive_json(), timeout=PING_INTERVAL
                )
                client_message_json_demo = (
                    str(client_message_json)[:80] + "..."
                    if len(str(client_message_json)) > 80
                    else str(client_message_json)
                )
                logger.debug(
                    f"WebSocket /ws/summarize endpoint received raw message: {client_message_json_demo} from {websocket.client}"
                )

                # Handle pong messages
                if (
                    client_message_json.get("type") == "pong"
                    and "task_id" in client_message_json
                ):
                    logger.debug(
                        f"Handling pong from {websocket.client} for task {client_message_json['task_id']} in /ws/summarize endpoint"
                    )
                    if task_id and client_message_json["task_id"] == task_id:
                        await task_queue.ws_manager.handle_pong(
                            client_message_json["task_id"], websocket
                        )
                        logger.debug(
                            f"Pong successfully processed for task {task_id} from {websocket.client} in /ws/summarize endpoint"
                        )
                    elif not task_id and client_message_json["task_id"] == "initial":
                        await task_queue.ws_manager.handle_pong(
                            client_message_json["task_id"], websocket
                        )
                        logger.debug(
                            f"Initial pong successfully processed from {websocket.client} in /ws/summarize endpoint"
                        )
                    else:
                        logger.warning(
                            f"Pong received for mismatched task_id from {websocket.client} in /ws/summarize endpoint: expected {task_id}, got {client_message_json['task_id']}"
                        )
                    continue

                # Note: client_uid is deprecated and ignored in processing
                # We still extract it for backward compatibility but don't use it
                if "client_uid" in client_message_json:
                    client_uid_for_error_reporting = client_message_json["client_uid"]
                    logger.debug(
                        f"Received deprecated client_uid: {client_uid_for_error_reporting} (ignored for processing)"
                    )

                logger.debug(
                    f"Attempting to parse WebSocketSummarizeRequest from {client_message_json_demo} for {websocket.client} in /ws/summarize endpoint"
                )
                request = WebSocketSummarizeRequest(**client_message_json)
                logger.debug(
                    f"Parsed WebSocketSummarizeRequest in /ws/summarize endpoint: type {request.type}, mode {request.mode} for {websocket.client}"
                )

                # Process request and get text to summarize
                text_to_summarize = _process_summarize_request(
                    request, websocket.client
                )

                mode = request.mode.value if request.mode else "default"
                logger.debug(
                    f"Attempting to add summarize task for {websocket.client} (mode: {mode}) in /ws/summarize endpoint."
                )
                response = await task_queue.add_summarize_task(
                    text_to_summarize,
                    mode=mode,
                    client_uid=None,  # Explicitly set to None - deprecated field
                )
                logger.debug(
                    f"Summarize task add response for {websocket.client} in /ws/summarize endpoint: ID {response.task_id}, Status {response.status}"
                )

                if response.task_id != "error":
                    if task_id != response.task_id:
                        if task_id:
                            logger.debug(
                                f"Task ID changed for {websocket.client} in /ws/summarize endpoint. Old: {task_id}, New: {response.task_id}. Disconnecting from old."
                            )
                            await task_queue.ws_manager.disconnect_from_task(
                                task_id, websocket
                            )
                    task_id = response.task_id
                    logger.debug(
                        f"Registering websocket {websocket.client} for new summarize task {task_id} in /ws/summarize endpoint"
                    )
                    await task_queue.register_websocket(task_id, websocket)

                await websocket.send_json(
                    WebSocketSummarizeMessage(
                        type=MessageType.STATUS,
                        task_id=response.task_id,
                        status=response.status,
                        summary=response.summary,
                        error=response.error,
                        client_uid=None,  # Don't include deprecated field in new responses
                    ).model_dump()
                )
                logger.debug(
                    f"Sent status update for summarize task {response.task_id} to {websocket.client} in /ws/summarize endpoint"
                )

            except asyncio.TimeoutError:
                logger.debug(
                    f"Timeout waiting for client message from {websocket.client} for summarize task {task_id} in /ws/summarize endpoint. Sending ping."
                )
                if task_id:
                    await websocket.send_json({"type": "ping", "task_id": task_id})
                    logger.debug(
                        f"Sent ping to client {websocket.client} for summarize task_id: {task_id} in /ws/summarize endpoint"
                    )
                else:
                    await websocket.send_json({"type": "ping", "task_id": "initial"})
                    logger.debug(
                        f"Sent initial ping to client {websocket.client} for summarize session in /ws/summarize endpoint."
                    )
                continue

            except ValueError as e:
                logger.warning(
                    f"ValueError in summarize websocket for {websocket.client}: {str(e)}",
                    exc_info=True,
                )
                await websocket.send_json(
                    WebSocketSummarizeMessage(
                        type=MessageType.STATUS,
                        task_id=task_id or "error",
                        status=TaskStatus.FAILED,
                        error=str(e),
                        client_uid=None,  # Don't include deprecated field in error responses
                    ).model_dump()
                )
                continue

    except WebSocketDisconnect:
        logger.info(
            f"WebSocket disconnected: {websocket.client} from summarize task {task_id if task_id else 'general session'} in /ws/summarize endpoint"
        )
    except Exception as e:
        current_task_id_for_log = task_id if task_id else "N/A"
        logger.error(
            f"WebSocket error in /ws/summarize for {websocket.client}, task {current_task_id_for_log}: {str(e)}",
            exc_info=True,
        )
        if websocket.client_state.name == "CONNECTED":
            try:
                await websocket.send_json(
                    WebSocketSummarizeMessage(
                        type=MessageType.STATUS,
                        task_id=current_task_id_for_log,
                        status=TaskStatus.FAILED,
                        error=f"Unexpected server error: {str(e)}",
                        client_uid=None,  # Don't include deprecated field in error responses
                    ).model_dump()
                )
                logger.debug(
                    f"Sent final error message to websocket for {websocket.client}, task {current_task_id_for_log} in /ws/summarize endpoint"
                )
            except Exception as send_ex:
                logger.error(
                    f"Failed to send final error message to websocket for {websocket.client}, task {current_task_id_for_log} in /ws/summarize endpoint: {send_ex}",
                    exc_info=True,
                )
    finally:
        logger.debug(
            f"Closing WebSocket /ws/summarize for {websocket.client}. Task ID was: {task_id}"
        )
        await task_queue.ws_manager.disconnect_websocket(websocket)
        logger.debug(
            f"Called disconnect_websocket for {websocket.client} in /ws/summarize endpoint finally block."
        )
